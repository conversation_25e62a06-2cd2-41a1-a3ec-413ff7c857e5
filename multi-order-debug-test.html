<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单模块深度调试测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 120px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a8a;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            flex: 1;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007cba;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 多订单模块深度调试测试</h1>
        
        <div class="section">
            <h3>📝 输入测试数据</h3>
            <textarea id="testInput" placeholder="在此输入多订单内容进行测试...">接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639
送机：团号：EJBTBY250716-3 1PAX 21/7 农玉琴 1045 KLIA2 OUT (AK168)
送机：团号：EJBTBY250716-4 2PAX 21/7 甘小姐 0805 KLIA2 OUT (AK116)
举牌接机：团号：MEET001 1PAX 17/7 KLIA IN 1530 客人：李先生</textarea>
            
            <div>
                <button onclick="runFullDiagnostic()">🔬 完整诊断</button>
                <button onclick="testGeminiDirectly()">🤖 直接测试Gemini</button>
                <button onclick="testRealtimeManager()">⚡ 测试实时分析器</button>
                <button onclick="testMultiOrderManager()">📋 测试多订单管理器</button>
                <button onclick="clearAll()" class="danger">🧹 清空所有</button>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="orderCount">-</div>
                    <div>检测到订单数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="isMultiOrder">-</div>
                    <div>是否多订单</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="confidence">-</div>
                    <div>置信度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="responseTime">-</div>
                    <div>响应时间(ms)</div>
                </div>
            </div>
        </div>

        <div class="grid">
            <div class="section">
                <h3>📊 Gemini分析结果</h3>
                <div id="geminiResult" class="result">等待测试结果...</div>
            </div>
            
            <div class="section">
                <h3>🎯 多订单面板状态</h3>
                <div id="panelStatus" class="result">等待检查...</div>
            </div>
        </div>

        <div class="section">
            <h3>📋 实时日志</h3>
            <div id="debugLog" class="log">等待调试信息...\n</div>
        </div>

        <div class="section">
            <h3>🔧 系统状态</h3>
            <div id="systemStatus" class="result">检查系统组件...</div>
        </div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 调试工具
        let debugState = {
            startTime: null,
            lastGeminiResult: null,
            testInput: null
        };

        // DOM元素
        const elements = {
            testInput: document.getElementById('testInput'),
            geminiResult: document.getElementById('geminiResult'),
            panelStatus: document.getElementById('panelStatus'),
            debugLog: document.getElementById('debugLog'),
            systemStatus: document.getElementById('systemStatus'),
            orderCount: document.getElementById('orderCount'),
            isMultiOrder: document.getElementById('isMultiOrder'),
            confidence: document.getElementById('confidence'),
            responseTime: document.getElementById('responseTime')
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            elements.debugLog.textContent += logEntry;
            elements.debugLog.scrollTop = elements.debugLog.scrollHeight;
            console.log(`[DEBUG-${type.toUpperCase()}] ${message}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 深度调试工具初始化中...', 'info');
            checkSystemComponents();
            setupEventListeners();
            log('✅ 调试工具初始化完成', 'success');
        });

        // 检查系统组件
        function checkSystemComponents() {
            log('🔍 检查系统组件状态...', 'info');
            
            const components = {
                'OTA命名空间': window.OTA,
                'Logger服务': window.OTA?.logger || window.logger,
                'AppState服务': window.OTA?.appState || window.appState,
                'GeminiService服务': window.OTA?.geminiService || window.geminiService,
                'RealtimeAnalysisManager': window.OTA?.realtimeAnalysisManager,
                'MultiOrderManager': window.OTA?.multiOrderManager || window.MultiOrderManager
            };

            let statusHTML = '';
            for (const [name, component] of Object.entries(components)) {
                const status = component ? '✅ 已加载' : '❌ 未找到';
                statusHTML += `${name}: ${status}\n`;
                log(`${name}: ${status}`, component ? 'success' : 'error');
            }
            
            elements.systemStatus.textContent = statusHTML;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 监听多订单检测事件
            document.addEventListener('multiOrderDetected', function(event) {
                log('🔔 收到 multiOrderDetected 事件', 'success');
                log(`事件详情: ${JSON.stringify(event.detail, null, 2)}`, 'info');
                
                const detail = event.detail;
                if (detail && detail.multiOrderResult) {
                    updateStats(detail.multiOrderResult);
                    checkPanelDisplay();
                }
            });

            log('📡 事件监听器已设置', 'info');
        }

        // 完整诊断
        async function runFullDiagnostic() {
            log('🔬 开始完整诊断流程...', 'info');
            debugState.startTime = Date.now();
            debugState.testInput = elements.testInput.value.trim();

            if (!debugState.testInput) {
                log('❌ 测试输入为空', 'error');
                return;
            }

            // 1. 直接测试Gemini
            await testGeminiDirectly();
            
            // 2. 测试实时分析器
            await testRealtimeManager();
            
            // 3. 检查多订单面板状态
            checkPanelDisplay();
            
            log('🎯 完整诊断流程完成', 'success');
        }

        // 直接测试Gemini服务
        async function testGeminiDirectly() {
            log('🤖 直接测试Gemini服务...', 'info');
            
            const geminiService = window.OTA?.geminiService || window.geminiService;
            if (!geminiService) {
                log('❌ GeminiService未找到', 'error');
                return;
            }

            try {
                const startTime = Date.now();
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(
                    debugState.testInput, 
                    { enabled: true, attempts: 3, consistencyThreshold: 0.8 }
                );
                const responseTime = Date.now() - startTime;

                debugState.lastGeminiResult = result;
                
                log(`✅ Gemini分析完成，用时: ${responseTime}ms`, 'success');
                log(`结果: ${JSON.stringify(result, null, 2)}`, 'info');

                elements.geminiResult.textContent = JSON.stringify(result, null, 2);
                updateStats(result, responseTime);

                // 检查结果是否符合多订单触发条件
                if (result.orderCount > 1) {
                    log(`🎯 应该触发多订单模式：orderCount=${result.orderCount}`, 'success');
                } else if (result.orderCount === 1) {
                    log(`📝 应该触发单订单模式：orderCount=${result.orderCount}`, 'info');
                } else {
                    log(`⚠️ 无有效订单：orderCount=${result.orderCount}`, 'warning');
                }

            } catch (error) {
                log(`❌ Gemini分析失败: ${error.message}`, 'error');
                elements.geminiResult.textContent = `错误: ${error.message}`;
            }
        }

        // 测试实时分析管理器
        async function testRealtimeManager() {
            log('⚡ 测试实时分析管理器...', 'info');
            
            const realtimeManager = window.OTA?.realtimeAnalysisManager;
            if (!realtimeManager) {
                log('❌ RealtimeAnalysisManager未找到', 'error');
                return;
            }

            try {
                // 模拟输入事件
                const fakeEvent = {
                    target: {
                        value: debugState.testInput
                    }
                };

                log('📝 模拟触发实时分析...', 'info');
                
                // 直接调用分析方法
                await realtimeManager.handleRealtimeAnalysis(debugState.testInput);
                
                log('✅ 实时分析管理器测试完成', 'success');

            } catch (error) {
                log(`❌ 实时分析管理器测试失败: ${error.message}`, 'error');
            }
        }

        // 测试多订单管理器
        function testMultiOrderManager() {
            log('📋 测试多订单管理器...', 'info');
            
            const multiOrderManager = window.OTA?.multiOrderManager || window.MultiOrderManager;
            if (!multiOrderManager) {
                log('❌ MultiOrderManager未找到', 'error');
                return;
            }

            if (!debugState.lastGeminiResult) {
                log('⚠️ 没有Gemini结果，先运行Gemini测试', 'warning');
                return;
            }

            try {
                log('📡 手动触发多订单检测事件...', 'info');
                
                // 手动触发事件
                const event = new CustomEvent('multiOrderDetected', {
                    detail: {
                        multiOrderResult: debugState.lastGeminiResult,
                        orderText: debugState.testInput
                    }
                });
                
                document.dispatchEvent(event);
                
                log('✅ 多订单检测事件已触发', 'success');

            } catch (error) {
                log(`❌ 多订单管理器测试失败: ${error.message}`, 'error');
            }
        }

        // 检查多订单面板显示状态
        function checkPanelDisplay() {
            log('🎯 检查多订单面板状态...', 'info');
            
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) {
                log('❌ 多订单面板元素未找到', 'error');
                elements.panelStatus.textContent = '❌ 面板元素未找到';
                return;
            }

            const isVisible = panel.style.display !== 'none';
            const hasContent = panel.innerHTML.trim().length > 0;
            
            const statusInfo = {
                '面板存在': '✅ 是',
                '可见状态': isVisible ? '✅ 显示' : '❌ 隐藏',
                'CSS类名': panel.className || '无',
                '内容长度': `${panel.innerHTML.length} 字符`,
                '有内容': hasContent ? '✅ 是' : '❌ 否'
            };

            let statusHTML = '';
            for (const [key, value] of Object.entries(statusInfo)) {
                statusHTML += `${key}: ${value}\n`;
            }
            
            elements.panelStatus.textContent = statusHTML;
            
            log(`多订单面板状态 - 可见: ${isVisible}, 有内容: ${hasContent}`, isVisible ? 'success' : 'warning');
        }

        // 更新统计信息
        function updateStats(result, responseTime = null) {
            if (!result) return;

            elements.orderCount.textContent = result.orderCount || 0;
            elements.isMultiOrder.textContent = result.isMultiOrder ? '✅ 是' : '❌ 否';
            elements.confidence.textContent = result.confidence ? 
                `${(result.confidence * 100).toFixed(1)}%` : 'N/A';
            
            if (responseTime !== null) {
                elements.responseTime.textContent = responseTime;
            }
        }

        // 清空所有
        function clearAll() {
            elements.geminiResult.textContent = '等待测试结果...';
            elements.panelStatus.textContent = '等待检查...';
            elements.debugLog.textContent = '等待调试信息...\n';
            elements.orderCount.textContent = '-';
            elements.isMultiOrder.textContent = '-';
            elements.confidence.textContent = '-';
            elements.responseTime.textContent = '-';
            
            debugState.lastGeminiResult = null;
            
            log('🧹 所有内容已清空', 'info');
        }
    </script>
</body>
</html>
