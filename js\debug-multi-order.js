/**
 * 多订单检测调试工具
 * 专门用于诊断 GeminiService.detectAndSplitMultiOrders() 方法的问题
 */

// 全局调试状态
let debugState = {
    isDebugging: false,
    startTime: null,
    lastResult: null,
    logBuffer: []
};

// DOM元素引用
let elements = {};

/**
 * 初始化调试工具
 */
document.addEventListener('DOMContentLoaded', function() {
    log('🔧 调试工具初始化中...', 'info');
    
    // 获取DOM元素引用
    elements = {
        orderTextInput: document.getElementById('orderTextInput'),
        resultDisplay: document.getElementById('resultDisplay'),
        logDisplay: document.getElementById('logDisplay'),
        statusDisplay: document.getElementById('statusDisplay'),
        charCount: document.getElementById('charCount'),
        orderCount: document.getElementById('orderCount'),
        confidence: document.getElementById('confidence'),
        responseTime: document.getElementById('responseTime')
    };
    
    // 绑定输入事件
    if (elements.orderTextInput) {
        elements.orderTextInput.addEventListener('input', updateCharCount);
        updateCharCount(); // 初始化字符计数
    }
    
    // 初始化依赖服务
    initializeServices();
    
    // 设置多订单事件监听器
    setupEventListeners();
    
    log('✅ 调试工具初始化完成', 'success');
});

/**
 * 初始化依赖服务
 */
function initializeServices() {
    try {
        // 确保OTA命名空间存在
        window.OTA = window.OTA || {};
        
        // 初始化Logger
        if (window.getLogger) {
            window.OTA.logger = window.getLogger();
            log('✅ Logger服务已初始化', 'info');
        }
        
        // 初始化AppState
        if (window.getAppState) {
            window.OTA.appState = window.getAppState();
            log('✅ AppState服务已初始化', 'info');
        }
        
        // 初始化GeminiService
        if (window.getGeminiService) {
            window.OTA.geminiService = window.getGeminiService();
            log('✅ GeminiService服务已初始化', 'info');
        } else {
            log('❌ GeminiService服务未找到', 'error');
        }
        
    } catch (error) {
        log(`❌ 服务初始化失败: ${error.message}`, 'error');
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 监听多订单检测事件
    document.addEventListener('multiOrderDetected', function(event) {
        log('🔔 收到 multiOrderDetected 事件', 'success');
        log(`事件详情: ${JSON.stringify(event.detail, null, 2)}`, 'info');
    });
    
    log('📡 事件监听器已设置', 'info');
}

/**
 * 更新字符计数
 */
function updateCharCount() {
    if (elements.orderTextInput && elements.charCount) {
        const count = elements.orderTextInput.value.length;
        elements.charCount.textContent = count.toLocaleString();
        
        // 检查最小长度要求
        if (count < 50) {
            elements.charCount.style.color = '#dc3545';
        } else {
            elements.charCount.style.color = '#667eea';
        }
    }
}

/**
 * 开始多订单检测
 */
async function startDetection() {
    if (debugState.isDebugging) {
        log('⚠️ 检测正在进行中，请等待...', 'warning');
        return;
    }
    
    const orderText = elements.orderTextInput.value.trim();
    
    if (!orderText) {
        showStatus('请输入要检测的订单文本', 'error');
        return;
    }
    
    if (orderText.length < 50) {
        showStatus('输入文本长度不足50字符，可能无法正确检测', 'error');
        return;
    }
    
    debugState.isDebugging = true;
    debugState.startTime = Date.now();
    
    showStatus('正在进行多订单检测...', 'loading');
    log(`🚀 开始检测，文本长度: ${orderText.length} 字符`, 'info');
    log('------- 检测开始 -------', 'info');
    
    try {
        // 检查GeminiService是否可用
        const geminiService = window.OTA?.geminiService || window.getGeminiService?.();
        if (!geminiService) {
            throw new Error('GeminiService 未初始化或不可用');
        }
        
        log('✅ GeminiService 可用，开始调用 detectAndSplitMultiOrders()', 'info');
        
        // 调用检测方法
        const result = await geminiService.detectAndSplitMultiOrders(orderText);
        
        const endTime = Date.now();
        const responseTime = endTime - debugState.startTime;
        
        debugState.lastResult = result;
        
        log(`✅ 检测完成，耗时: ${responseTime}ms`, 'success');
        log('------- 检测结果 -------', 'info');
        log(`是否多订单: ${result.isMultiOrder}`, 'info');
        log(`订单数量: ${result.orderCount}`, 'info');
        log(`置信度: ${result.confidence}`, 'info');
        log(`分析说明: ${result.analysis}`, 'info');
        
        // 更新显示
        updateResultDisplay(result);
        updateStats(result, responseTime);
        
        if (result.isMultiOrder && result.orderCount > 1) {
            showStatus(`检测成功！发现 ${result.orderCount} 个订单`, 'success');
            log(`📋 订单详情:`, 'info');
            result.orders.forEach((order, index) => {
                log(`订单 ${index + 1}: ${order.customerName} - ${order.pickup} → ${order.dropoff}`, 'info');
            });
        } else {
            showStatus(`检测完成，判定为单订单 (${result.orderCount}个)`, 'success');
        }
        
    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - debugState.startTime;
        
        log(`❌ 检测失败: ${error.message}`, 'error');
        log(`错误堆栈: ${error.stack}`, 'error');
        
        showStatus(`检测失败: ${error.message}`, 'error');
        updateStats(null, responseTime);
        
        // 显示错误信息
        elements.resultDisplay.textContent = `错误: ${error.message}\n\n堆栈信息:\n${error.stack}`;
        
    } finally {
        debugState.isDebugging = false;
        log('------- 检测结束 -------', 'info');
    }
}

/**
 * 测试事件触发
 */
function testEvent() {
    if (!debugState.lastResult) {
        showStatus('请先进行一次检测以获取结果', 'error');
        return;
    }
    
    log('📡 模拟触发 multiOrderDetected 事件', 'info');
    
    const event = new CustomEvent('multiOrderDetected', {
        detail: {
            multiOrderResult: debugState.lastResult,
            orderText: elements.orderTextInput.value.trim()
        }
    });
    
    document.dispatchEvent(event);
    
    showStatus('事件已触发，检查日志以查看处理结果', 'success');
}

/**
 * 更新结果显示
 */
function updateResultDisplay(result) {
    if (elements.resultDisplay) {
        elements.resultDisplay.textContent = JSON.stringify(result, null, 2);
    }
}

/**
 * 更新统计信息
 */
function updateStats(result, responseTime) {
    if (elements.orderCount) {
        elements.orderCount.textContent = result ? result.orderCount : 'ERROR';
    }
    
    if (elements.confidence) {
        const confidence = result ? (result.confidence * 100).toFixed(1) + '%' : 'ERROR';
        elements.confidence.textContent = confidence;
    }
    
    if (elements.responseTime) {
        elements.responseTime.textContent = responseTime.toLocaleString();
    }
}

/**
 * 显示状态信息
 */
function showStatus(message, type) {
    if (elements.statusDisplay) {
        elements.statusDisplay.textContent = message;
        elements.statusDisplay.className = `status ${type}`;
        elements.statusDisplay.style.display = 'block';
        
        // 自动隐藏成功和错误状态
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                elements.statusDisplay.style.display = 'none';
            }, 5000);
        }
    }
}

/**
 * 清空日志
 */
function clearLogs() {
    debugState.logBuffer = [];
    if (elements.logDisplay) {
        elements.logDisplay.textContent = '日志已清空...\n';
    }
    log('🧹 日志已清空', 'info');
}

/**
 * 重置所有
 */
function resetAll() {
    // 重置状态
    debugState.isDebugging = false;
    debugState.startTime = null;
    debugState.lastResult = null;
    
    // 清空显示
    if (elements.resultDisplay) {
        elements.resultDisplay.textContent = '等待检测结果...';
    }
    
    if (elements.statusDisplay) {
        elements.statusDisplay.style.display = 'none';
    }
    
    // 重置统计
    if (elements.orderCount) elements.orderCount.textContent = '-';
    if (elements.confidence) elements.confidence.textContent = '-';
    if (elements.responseTime) elements.responseTime.textContent = '-';
    
    // 清空日志
    clearLogs();
    
    log('🔄 所有状态已重置', 'info');
}

/**
 * 日志记录函数
 */
function log(message, level = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    
    // 添加到缓冲区
    debugState.logBuffer.push(logEntry);
    
    // 保持缓冲区大小
    if (debugState.logBuffer.length > 1000) {
        debugState.logBuffer = debugState.logBuffer.slice(-500);
    }
    
    // 更新显示
    if (elements.logDisplay) {
        elements.logDisplay.textContent = debugState.logBuffer.join('\n') + '\n';
        elements.logDisplay.scrollTop = elements.logDisplay.scrollHeight;
    }
    
    // 同时输出到控制台
    console.log(`[DEBUG] ${logEntry}`);
}

/**
 * 全局函数暴露
 */
window.startDetection = startDetection;
window.testEvent = testEvent;
window.clearLogs = clearLogs;
window.resetAll = resetAll;