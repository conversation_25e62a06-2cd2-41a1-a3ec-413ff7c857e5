<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Multi-Order Detection Test</title>
</head>
<body>
    <h1>Multi-Order Detection Test</h1>
    <textarea id="testInput" rows="10" cols="80">[2025/7/10 17:27] Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：

团号：EJBTBY250710-1
2PAX
14/7 THE FACE STYLE HOTEL KL 0500AM - KLIA2 (AK5136 0915)
客人：刘凯
客人联系：18764221412
[2025/7/11 19:22] Joshua: 接机：

团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028</textarea>
    <br>
    <button onclick="testDetection()">Test Multi-Order Detection</button>
    <div id="result"></div>

    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>

    <script>
        async function testDetection() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const geminiService = getGeminiService();
                const result = await geminiService.detectAndSplitMultiOrders(input);
                
                resultDiv.innerHTML = `
                    <h3>Detection Result:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                    <p>isMultiOrder: ${result.isMultiOrder}</p>
                    <p>orderCount: ${result.orderCount}</p>
                    <p>confidence: ${result.confidence}</p>
                    <p>analysis: ${result.analysis}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
                console.error('Detection error:', error);
            }
        }

        // Initialize services
        window.OTA = window.OTA || {};
        window.OTA.appState = getAppState();
        window.OTA.logger = getLogger();
    </script>
</body>
</html>