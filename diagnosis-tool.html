<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单问题深度诊断与修复</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .step-number {
            background: #007cba;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 120px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a8a;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #218838;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        .stats {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            flex: 1;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007cba;
        }
        .issue-list {
            list-style: none;
            padding: 0;
        }
        .issue-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .issue-item.fixed {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .fix-btn {
            margin-left: auto;
            padding: 5px 10px;
            font-size: 12px;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 多订单模块问题深度诊断与修复工具</h1>
        
        <div class="section">
            <h3><span class="step-number">1</span>问题描述确认</h3>
            <div class="status info">
                <strong>问题现象：</strong>输入多订单内容，但多订单模块没有被触发<br>
                <strong>预期行为：</strong>Gemini分析出多订单 → 触发multiOrderDetected事件 → 显示多订单面板<br>
                <strong>可能原因：</strong>Gemini提示词问题、多订单检测条件不匹配、事件传递失败、面板显示问题
            </div>
        </div>

        <div class="section">
            <h3><span class="step-number">2</span>测试数据输入</h3>
            <textarea id="testInput" placeholder="在此输入多订单内容进行诊断...">接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639
送机：团号：EJBTBY250716-3 1PAX 21/7 农玉琴 1045 KLIA2 OUT (AK168)
送机：团号：EJBTBY250716-4 2PAX 21/7 甘小姐 0805 KLIA2 OUT (AK116)
举牌接机：团号：MEET001 1PAX 17/7 KLIA IN 1530 客人：李先生 paging service needed</textarea>
            
            <div>
                <button onclick="runFullDiagnosis()" class="success">🔬 开始完整诊断</button>
                <button onclick="testPromptOnly()">📝 仅测试提示词</button>
                <button onclick="testEventFlow()">📡 测试事件流</button>
                <button onclick="testPanelDisplay()">🎭 测试面板显示</button>
                <button onclick="autoFix()" class="success">🔧 自动修复</button>
                <button onclick="clearAll()" class="danger">🧹 清空</button>
            </div>
        </div>

        <div class="grid">
            <div class="section">
                <h3><span class="step-number">3</span>Gemini分析结果</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value" id="orderCount">-</div>
                        <div>订单数量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="isMultiOrder">-</div>
                        <div>多订单标记</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="confidence">-</div>
                        <div>置信度</div>
                    </div>
                </div>
                <div id="geminiResult" class="result">等待测试结果...</div>
            </div>
            
            <div class="section">
                <h3><span class="step-number">4</span>事件流程跟踪</h3>
                <div id="eventFlow" class="result">等待事件追踪...</div>
            </div>
        </div>

        <div class="section">
            <h3><span class="step-number">5</span>问题发现与修复</h3>
            <ul id="issuesList" class="issue-list">
                <li class="issue-item">
                    🔍 系统初始化检查
                    <button class="fix-btn" onclick="checkSystemInit()">检查</button>
                </li>
                <li class="issue-item">
                    🤖 Gemini提示词验证
                    <button class="fix-btn" onclick="validatePrompt()">验证</button>
                </li>
                <li class="issue-item">
                    📡 事件监听器状态
                    <button class="fix-btn" onclick="checkEventListeners()">检查</button>
                </li>
                <li class="issue-item">
                    🎭 面板元素检查
                    <button class="fix-btn" onclick="checkPanelElement()">检查</button>
                </li>
                <li class="issue-item">
                    ⚙️ 触发条件验证
                    <button class="fix-btn" onclick="validateTriggerConditions()">验证</button>
                </li>
            </ul>
        </div>

        <div class="section">
            <h3><span class="step-number">6</span>实时调试日志</h3>
            <div id="debugLog" class="log">等待调试信息...\n</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 诊断状态
        let diagnosisState = {
            startTime: null,
            testInput: null,
            geminiResult: null,
            eventTraces: [],
            foundIssues: [],
            fixedIssues: []
        };

        // DOM元素
        const elements = {
            testInput: document.getElementById('testInput'),
            geminiResult: document.getElementById('geminiResult'),
            eventFlow: document.getElementById('eventFlow'),
            debugLog: document.getElementById('debugLog'),
            orderCount: document.getElementById('orderCount'),
            isMultiOrder: document.getElementById('isMultiOrder'),
            confidence: document.getElementById('confidence'),
            issuesList: document.getElementById('issuesList')
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            elements.debugLog.textContent += logEntry;
            elements.debugLog.scrollTop = elements.debugLog.scrollHeight;
            console.log(`[DIAGNOSIS-${type.toUpperCase()}] ${message}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 多订单问题诊断工具初始化...', 'info');
            setupEventMonitoring();
            log('✅ 诊断工具就绪', 'success');
        });

        // 事件监控设置
        function setupEventMonitoring() {
            // 监听多订单检测事件
            document.addEventListener('multiOrderDetected', function(event) {
                const trace = {
                    timestamp: Date.now(),
                    event: 'multiOrderDetected',
                    detail: event.detail
                };
                diagnosisState.eventTraces.push(trace);
                log('🔔 监听到 multiOrderDetected 事件', 'success');
                updateEventFlow();
            });

            // 监听其他相关事件
            ['orderStateChange', 'analysisComplete', 'panelShow', 'panelHide'].forEach(eventName => {
                document.addEventListener(eventName, function(event) {
                    const trace = {
                        timestamp: Date.now(),
                        event: eventName,
                        detail: event.detail || {}
                    };
                    diagnosisState.eventTraces.push(trace);
                    log(`📡 监听到 ${eventName} 事件`, 'info');
                    updateEventFlow();
                });
            });
        }

        // 更新事件流显示
        function updateEventFlow() {
            const traces = diagnosisState.eventTraces.slice(-10); // 只显示最近10个事件
            const flowText = traces.map(trace => {
                const time = new Date(trace.timestamp).toLocaleTimeString();
                return `[${time}] ${trace.event}`;
            }).join('\n');
            elements.eventFlow.textContent = flowText || '暂无事件记录';
        }

        // 完整诊断流程
        async function runFullDiagnosis() {
            log('🔬 开始完整诊断流程...', 'info');
            diagnosisState.startTime = Date.now();
            diagnosisState.testInput = elements.testInput.value.trim();
            diagnosisState.eventTraces = [];

            if (!diagnosisState.testInput) {
                log('❌ 测试输入为空', 'error');
                return;
            }

            try {
                // 步骤1：系统检查
                await checkSystemInit();
                
                // 步骤2：测试Gemini分析
                await testGeminiAnalysis();
                
                // 步骤3：检查事件监听器
                await checkEventListeners();
                
                // 步骤4：检查面板元素
                await checkPanelElement();
                
                // 步骤5：验证触发条件
                await validateTriggerConditions();
                
                // 步骤6：模拟完整流程
                await simulateFullFlow();
                
                log('✅ 完整诊断流程完成', 'success');
                
            } catch (error) {
                log(`❌ 诊断过程出错: ${error.message}`, 'error');
            }
        }

        // 检查系统初始化
        async function checkSystemInit() {
            log('🔍 检查系统组件初始化状态...', 'info');
            
            const components = {
                'window.OTA': !!window.OTA,
                'window.OTA.logger': !!(window.OTA?.logger),
                'window.OTA.geminiService': !!(window.OTA?.geminiService),
                'window.OTA.multiOrderManager': !!(window.OTA?.multiOrderManager),
                'window.OTA.realtimeAnalysisManager': !!(window.OTA?.realtimeAnalysisManager),
                'document.getElementById("multiOrderPanel")': !!document.getElementById('multiOrderPanel')
            };

            let allGood = true;
            for (const [name, status] of Object.entries(components)) {
                log(`${status ? '✅' : '❌'} ${name}`, status ? 'success' : 'error');
                if (!status) allGood = false;
            }

            if (allGood) {
                markIssueFixed('系统初始化检查');
            } else {
                addIssue('系统组件未完全初始化');
            }
        }

        // 测试Gemini分析
        async function testGeminiAnalysis() {
            log('🤖 测试Gemini多订单分析...', 'info');
            
            const geminiService = window.OTA?.geminiService;
            if (!geminiService) {
                log('❌ GeminiService未找到', 'error');
                addIssue('GeminiService服务缺失');
                return;
            }

            try {
                const startTime = Date.now();
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(
                    diagnosisState.testInput,
                    { enabled: true, attempts: 3, consistencyThreshold: 0.8 }
                );
                const responseTime = Date.now() - startTime;

                diagnosisState.geminiResult = result;
                
                log(`✅ Gemini分析完成，用时: ${responseTime}ms`, 'success');
                log(`📊 分析结果: orderCount=${result.orderCount}, isMultiOrder=${result.isMultiOrder}`, 'info');

                // 更新显示
                elements.geminiResult.textContent = JSON.stringify(result, null, 2);
                elements.orderCount.textContent = result.orderCount || 0;
                elements.isMultiOrder.textContent = result.isMultiOrder ? '✅ 是' : '❌ 否';
                elements.confidence.textContent = result.confidence ? 
                    `${(result.confidence * 100).toFixed(1)}%` : 'N/A';

                // 分析结果
                if (result.orderCount > 1) {
                    log('🎯 Gemini正确识别为多订单', 'success');
                    markIssueFixed('Gemini提示词验证');
                } else {
                    log('⚠️ Gemini未识别为多订单，可能是提示词问题', 'warning');
                    addIssue('Gemini提示词需要优化');
                }

            } catch (error) {
                log(`❌ Gemini分析失败: ${error.message}`, 'error');
                addIssue('Gemini服务调用失败');
                elements.geminiResult.textContent = `错误: ${error.message}`;
            }
        }

        // 检查事件监听器
        async function checkEventListeners() {
            log('📡 检查事件监听器状态...', 'info');
            
            // 检查是否有multiOrderDetected监听器
            const hasListeners = diagnosisState.eventTraces.length > 0;
            
            if (hasListeners) {
                log('✅ 事件监听器正常工作', 'success');
                markIssueFixed('事件监听器状态');
            } else {
                log('⚠️ 事件监听器可能未正确绑定', 'warning');
                addIssue('事件监听器绑定异常');
            }
        }

        // 检查面板元素
        async function checkPanelElement() {
            log('🎭 检查多订单面板元素...', 'info');
            
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) {
                log('❌ 多订单面板元素不存在', 'error');
                addIssue('多订单面板元素缺失');
                return;
            }

            const panelInfo = {
                '元素存在': '✅ 是',
                '当前类名': panel.className || '无',
                '是否隐藏': panel.classList.contains('hidden') ? '✅ 是' : '❌ 否',
                'CSS display': window.getComputedStyle(panel).display,
                '内容长度': `${panel.innerHTML.length} 字符`
            };

            for (const [key, value] of Object.entries(panelInfo)) {
                log(`${key}: ${value}`, 'info');
            }

            markIssueFixed('面板元素检查');
        }

        // 验证触发条件
        async function validateTriggerConditions() {
            log('⚙️ 验证多订单触发条件...', 'info');
            
            if (!diagnosisState.geminiResult) {
                log('⚠️ 需要先运行Gemini测试', 'warning');
                return;
            }

            const result = diagnosisState.geminiResult;
            const shouldTrigger = result.orderCount > 1;
            
            log(`订单数量: ${result.orderCount}`, 'info');
            log(`应该触发多订单: ${shouldTrigger ? '是' : '否'}`, shouldTrigger ? 'success' : 'warning');
            
            if (shouldTrigger) {
                markIssueFixed('触发条件验证');
            } else {
                addIssue('触发条件不满足（订单数量<=1）');
            }
        }

        // 模拟完整流程
        async function simulateFullFlow() {
            log('🔄 模拟完整的多订单触发流程...', 'info');
            
            if (!diagnosisState.geminiResult) {
                log('⚠️ 需要先获取Gemini结果', 'warning');
                return;
            }

            try {
                // 手动触发多订单检测事件
                log('📡 手动触发 multiOrderDetected 事件...', 'info');
                
                const event = new CustomEvent('multiOrderDetected', {
                    detail: {
                        multiOrderResult: diagnosisState.geminiResult,
                        orderText: diagnosisState.testInput
                    }
                });
                
                document.dispatchEvent(event);
                
                // 等待一下，看面板是否显示
                setTimeout(() => {
                    const panel = document.getElementById('multiOrderPanel');
                    const isVisible = panel && !panel.classList.contains('hidden');
                    
                    if (isVisible) {
                        log('✅ 多订单面板成功显示', 'success');
                    } else {
                        log('❌ 多订单面板未显示', 'error');
                        addIssue('面板显示逻辑异常');
                    }
                }, 1000);
                
            } catch (error) {
                log(`❌ 流程模拟失败: ${error.message}`, 'error');
                addIssue('完整流程执行异常');
            }
        }

        // 仅测试提示词
        async function testPromptOnly() {
            log('📝 仅测试Gemini提示词...', 'info');
            await testGeminiAnalysis();
        }

        // 测试事件流
        function testEventFlow() {
            log('📡 测试事件流传递...', 'info');
            
            if (!diagnosisState.geminiResult) {
                log('⚠️ 需要先运行Gemini分析', 'warning');
                return;
            }

            const event = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: diagnosisState.geminiResult,
                    orderText: diagnosisState.testInput
                }
            });
            
            document.dispatchEvent(event);
            log('✅ 测试事件已发送', 'success');
        }

        // 测试面板显示
        function testPanelDisplay() {
            log('🎭 直接测试面板显示...', 'info');
            
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) {
                log('❌ 面板元素不存在', 'error');
                return;
            }

            // 直接显示面板
            panel.classList.remove('hidden');
            panel.style.display = 'flex';
            
            log('✅ 面板已强制显示', 'success');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                panel.classList.add('hidden');
                panel.style.display = 'none';
                log('🚪 面板已自动隐藏', 'info');
            }, 3000);
        }

        // 自动修复
        async function autoFix() {
            log('🔧 开始自动修复过程...', 'info');
            
            // 首先运行诊断
            await runFullDiagnosis();
            
            // 然后尝试修复发现的问题
            for (const issue of diagnosisState.foundIssues) {
                log(`🔧 尝试修复: ${issue}`, 'info');
                
                if (issue.includes('Gemini提示词')) {
                    // 修复提示词问题 - 这里需要实际修改代码
                    log('⚠️ Gemini提示词需要手动优化', 'warning');
                }
                
                if (issue.includes('事件监听器')) {
                    // 重新绑定事件监听器
                    setupEventMonitoring();
                    log('✅ 事件监听器已重新绑定', 'success');
                }
                
                if (issue.includes('触发条件')) {
                    log('⚠️ 触发条件问题需要检查Gemini返回结果', 'warning');
                }
            }
            
            log('🔧 自动修复完成', 'success');
        }

        // 添加问题
        function addIssue(issue) {
            if (!diagnosisState.foundIssues.includes(issue)) {
                diagnosisState.foundIssues.push(issue);
                log(`❌ 发现问题: ${issue}`, 'error');
            }
        }

        // 标记问题已修复
        function markIssueFixed(issue) {
            if (!diagnosisState.fixedIssues.includes(issue)) {
                diagnosisState.fixedIssues.push(issue);
                log(`✅ 问题已修复: ${issue}`, 'success');
                
                // 更新UI中的问题列表
                const issueItems = elements.issuesList.querySelectorAll('.issue-item');
                issueItems.forEach(item => {
                    if (item.textContent.includes(issue)) {
                        item.classList.add('fixed');
                        const button = item.querySelector('.fix-btn');
                        if (button) {
                            button.textContent = '✅';
                            button.disabled = true;
                        }
                    }
                });
            }
        }

        // 清空所有内容
        function clearAll() {
            elements.geminiResult.textContent = '等待测试结果...';
            elements.eventFlow.textContent = '等待事件追踪...';
            elements.debugLog.textContent = '等待调试信息...\n';
            elements.orderCount.textContent = '-';
            elements.isMultiOrder.textContent = '-';
            elements.confidence.textContent = '-';
            
            diagnosisState.geminiResult = null;
            diagnosisState.eventTraces = [];
            diagnosisState.foundIssues = [];
            diagnosisState.fixedIssues = [];
            
            // 重置问题列表
            const issueItems = elements.issuesList.querySelectorAll('.issue-item');
            issueItems.forEach(item => {
                item.classList.remove('fixed');
                const button = item.querySelector('.fix-btn');
                if (button) {
                    button.textContent = '检查';
                    button.disabled = false;
                }
            });
            
            log('🧹 所有内容已清空', 'info');
        }

        // 单独的功能函数
        function validatePrompt() {
            testGeminiAnalysis();
        }

        function checkEventListeners() {
            checkEventListeners();
        }

        function checkPanelElement() {
            checkPanelElement();
        }

        function validateTriggerConditions() {
            validateTriggerConditions();
        }
    </script>
</body>
</html>
