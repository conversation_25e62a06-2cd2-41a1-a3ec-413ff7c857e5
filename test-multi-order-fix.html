<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后多订单测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-result {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a8a;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 修复后多订单模块测试</h1>
        
        <div class="test-case">
            <h3>📝 测试案例1：多团号多日期订单</h3>
            <textarea class="test-input" id="test1">接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639
送机：团号：EJBTBY250716-3 1PAX 21/7 农玉琴 1045 KLIA2 OUT (AK168)
送机：团号：EJBTBY250716-4 2PAX 21/7 甘小姐 0805 KLIA2 OUT (AK116)</textarea>
            <button onclick="testCase(1)">🔬 测试</button>
            <div id="result1" class="test-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>🎭 测试案例2：举牌服务订单</h3>
            <textarea class="test-input" id="test2">举牌接机：团号：MEET001 1PAX 17/7 KLIA IN 1530 客人：李先生 paging service needed</textarea>
            <button onclick="testCase(2)">🔬 测试</button>
            <div id="result2" class="test-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>🔗 测试案例3：模拟完整流程</h3>
            <textarea class="test-input" id="test3">接机：团号：TEST001 2PAX 18/7 KLIA2 IN 1400 客人：王女士
送机：团号：TEST001 2PAX 22/7 王女士 0900 KLIA2 OUT 
包车：团号：TOUR002 4PAX 19/7 吉隆坡一日游 中文司机</textarea>
            <button onclick="testCase(3)">🔬 测试</button>
            <button onclick="testFullFlow(3)">🎯 测试完整流程</button>
            <div id="result3" class="test-result">等待测试...</div>
        </div>

        <div class="test-case">
            <h3>📊 系统状态检查</h3>
            <button onclick="checkSystem()">🔍 检查系统</button>
            <button onclick="testEventFlow()">📡 测试事件流</button>
            <button onclick="checkPanel()">🎭 检查面板</button>
            <div id="systemStatus" class="test-result">点击检查系统状态...</div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        // 测试结果存储
        let testResults = {};

        // 监听多订单检测事件
        document.addEventListener('multiOrderDetected', function(event) {
            console.log('🔔 收到 multiOrderDetected 事件:', event.detail);
            
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = `✅ multiOrderDetected 事件已触发！orderCount: ${event.detail.multiOrderResult?.orderCount}`;
            document.body.appendChild(successDiv);
            
            // 3秒后移除
            setTimeout(() => successDiv.remove(), 3000);
        });

        // 测试单个案例
        async function testCase(caseNumber) {
            const input = document.getElementById(`test${caseNumber}`).value;
            const resultDiv = document.getElementById(`result${caseNumber}`);
            
            resultDiv.textContent = '🔄 正在测试...';
            
            try {
                // 检查Gemini服务
                const geminiService = window.OTA?.geminiService;
                if (!geminiService) {
                    throw new Error('GeminiService未找到');
                }

                // 执行多订单检测
                const startTime = Date.now();
                const result = await geminiService.detectAndSplitMultiOrdersWithVerification(input);
                const duration = Date.now() - startTime;

                // 存储结果
                testResults[`case${caseNumber}`] = result;

                // 显示结果
                const output = `✅ 测试完成 (${duration}ms)
📊 分析结果:
- isMultiOrder: ${result.isMultiOrder}
- orderCount: ${result.orderCount}
- confidence: ${(result.confidence * 100).toFixed(1)}%
- orders数组长度: ${result.orders?.length || 0}

🎯 触发条件检查:
${result.orderCount > 1 ? '✅ 应该触发多订单模式' : '❌ 不会触发多订单模式'}
${result.isMultiOrder ? '✅ 标记为多订单' : '❌ 未标记为多订单'}

📝 详细结果:
${JSON.stringify(result, null, 2)}`;

                resultDiv.textContent = output;

                // 如果应该触发多订单，显示成功提示
                if (result.orderCount > 1) {
                    const successDiv = document.createElement('div');
                    successDiv.className = 'success';
                    successDiv.textContent = `✅ 案例${caseNumber}应该能触发多订单模式！`;
                    resultDiv.parentElement.appendChild(successDiv);
                    setTimeout(() => successDiv.remove(), 5000);
                }

            } catch (error) {
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.textContent = `❌ 案例${caseNumber}测试失败: ${error.message}`;
                resultDiv.parentElement.appendChild(errorDiv);
                setTimeout(() => errorDiv.remove(), 5000);
            }
        }

        // 测试完整流程
        async function testFullFlow(caseNumber) {
            const input = document.getElementById(`test${caseNumber}`).value;
            const resultDiv = document.getElementById(`result${caseNumber}`);
            
            resultDiv.textContent = '🔄 测试完整流程...';
            
            try {
                // 1. 先测试Gemini分析
                await testCase(caseNumber);
                
                // 2. 模拟实时分析管理器
                const realtimeManager = window.OTA?.realtimeAnalysisManager;
                if (realtimeManager && typeof realtimeManager.triggerRealtimeAnalysis === 'function') {
                    resultDiv.textContent += '\n\n🔄 模拟实时分析管理器...';
                    await realtimeManager.triggerRealtimeAnalysis(input);
                    resultDiv.textContent += '\n✅ 实时分析管理器完成';
                } else {
                    resultDiv.textContent += '\n⚠️ 实时分析管理器未找到';
                }

                // 3. 检查面板状态
                setTimeout(() => {
                    const panel = document.getElementById('multiOrderPanel');
                    if (panel) {
                        const isVisible = !panel.classList.contains('hidden');
                        resultDiv.textContent += `\n\n🎭 面板状态: ${isVisible ? '✅ 可见' : '❌ 隐藏'}`;
                    } else {
                        resultDiv.textContent += '\n⚠️ 多订单面板不存在';
                    }
                }, 2000);

            } catch (error) {
                resultDiv.textContent += `\n❌ 完整流程测试失败: ${error.message}`;
            }
        }

        // 检查系统状态
        function checkSystem() {
            const statusDiv = document.getElementById('systemStatus');
            
            const components = [
                ['window.OTA', !!window.OTA],
                ['window.OTA.geminiService', !!(window.OTA?.geminiService)],
                ['window.OTA.multiOrderManager', !!(window.OTA?.multiOrderManager)],
                ['window.OTA.realtimeAnalysisManager', !!(window.OTA?.realtimeAnalysisManager)],
                ['multiOrderPanel元素', !!document.getElementById('multiOrderPanel')]
            ];

            let status = '🔍 系统组件检查:\n';
            for (const [name, exists] of components) {
                status += `${exists ? '✅' : '❌'} ${name}\n`;
            }

            statusDiv.textContent = status;
        }

        // 测试事件流
        function testEventFlow() {
            const statusDiv = document.getElementById('systemStatus');
            
            statusDiv.textContent = '📡 测试事件流...\n';
            
            // 发送测试事件
            const testEvent = new CustomEvent('multiOrderDetected', {
                detail: {
                    multiOrderResult: {
                        isMultiOrder: true,
                        orderCount: 2,
                        confidence: 0.9,
                        orders: [{}, {}]
                    },
                    orderText: '测试数据'
                }
            });
            
            document.dispatchEvent(testEvent);
            
            statusDiv.textContent += '✅ 测试事件已发送\n等待事件响应...';
        }

        // 检查面板
        function checkPanel() {
            const statusDiv = document.getElementById('systemStatus');
            const panel = document.getElementById('multiOrderPanel');
            
            if (!panel) {
                statusDiv.textContent = '❌ 多订单面板元素不存在';
                return;
            }

            const info = `🎭 面板状态检查:
- 元素存在: ✅ 是
- 当前类名: ${panel.className}
- 是否隐藏: ${panel.classList.contains('hidden') ? '是' : '否'}
- CSS display: ${window.getComputedStyle(panel).display}
- 内容长度: ${panel.innerHTML.length} 字符`;

            statusDiv.textContent = info;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 多订单测试页面已加载');
            checkSystem();
        });
    </script>
</body>
</html>
