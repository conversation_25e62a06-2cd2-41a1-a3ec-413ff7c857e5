/**
 * 智能学习型格式预处理引擎 - 数据存储管理器
 * 负责学习系统数据的本地存储、版本控制和迁移
 * 集成到现有OTA系统存储架构中
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    /**
     * 学习系统数据存储管理器
     * 管理用户操作记录、学习规则、统计数据等的本地存储
     */
    class LearningStorageManager {
        constructor() {
            this.config = getLearningConfig();
            this.logger = getLogger();
            this.version = '1.0.0';
            
            // 数据结构版本控制
            this.dataVersion = {
                current: '1.0.0',
                supported: ['1.0.0'],
                migrationRequired: false
            };

            // 存储键配置
            this.storageKeys = this.config.get('storage.keys');
            
            // 初始化存储结构
            this.initializeStorage();
        }

        /**
         * 初始化存储结构
         * 检查现有数据并进行必要的迁移
         */
        initializeStorage() {
            try {
                // 检查数据版本
                this.checkDataVersion();
                
                // 初始化各个存储区域
                this.initializeUserOperations();
                this.initializeLearningRules();
                this.initializeSystemStats();
                this.initializeUserPreferences();
                
                this.logger?.log('学习系统存储初始化完成', 'info', {
                    version: this.version,
                    dataVersion: this.dataVersion.current
                });
                
            } catch (error) {
                this.logger?.logError('学习系统存储初始化失败', error);
            }
        }

        /**
         * 检查数据版本并执行迁移
         */
        checkDataVersion() {
            const versionKey = `${this.config.get('storage.keyPrefix')}version`;
            const storedVersion = localStorage.getItem(versionKey);
            
            if (!storedVersion) {
                // 首次安装
                localStorage.setItem(versionKey, this.dataVersion.current);
                return;
            }
            
            if (storedVersion !== this.dataVersion.current) {
                this.performDataMigration(storedVersion, this.dataVersion.current);
            }
        }

        /**
         * 执行数据迁移
         * @param {string} fromVersion - 源版本
         * @param {string} toVersion - 目标版本
         */
        performDataMigration(fromVersion, toVersion) {
            this.logger?.log(`执行数据迁移: ${fromVersion} -> ${toVersion}`, 'info');
            
            try {
                // 备份现有数据
                this.backupData(fromVersion);
                
                // 根据版本执行相应的迁移逻辑
                // 目前只有1.0.0版本，未来版本可在此添加迁移逻辑
                
                // 更新版本标记
                const versionKey = `${this.config.get('storage.keyPrefix')}version`;
                localStorage.setItem(versionKey, toVersion);
                
                this.logger?.log('数据迁移完成', 'success');
                
            } catch (error) {
                this.logger?.logError('数据迁移失败', error);
                throw error;
            }
        }

        /**
         * 备份数据
         * @param {string} version - 版本标识
         */
        backupData(version) {
            const backupKey = `${this.config.get('storage.keyPrefix')}backup-${version}-${Date.now()}`;
            const allData = this.exportAllData();
            
            try {
                localStorage.setItem(backupKey, JSON.stringify(allData));
                this.logger?.log(`数据备份完成: ${backupKey}`, 'info');
            } catch (error) {
                this.logger?.logError('数据备份失败', error);
            }
        }

        /**
         * 初始化用户操作记录存储
         */
        initializeUserOperations() {
            const key = this.storageKeys.userOperations;
            if (!localStorage.getItem(key)) {
                const initialData = {
                    version: this.dataVersion.current,
                    created: new Date().toISOString(),
                    operations: [],
                    metadata: {
                        totalOperations: 0,
                        lastOperation: null,
                        fields: {}
                    }
                };
                this.setData(key, initialData);
            }
        }

        /**
         * 初始化学习规则存储
         */
        initializeLearningRules() {
            const key = this.storageKeys.learningRules;
            if (!localStorage.getItem(key)) {
                const initialData = {
                    version: this.dataVersion.current,
                    created: new Date().toISOString(),
                    rules: [],
                    metadata: {
                        totalRules: 0,
                        lastGenerated: null,
                        categories: {}
                    }
                };
                this.setData(key, initialData);
            }
        }

        /**
         * 初始化系统统计存储
         */
        initializeSystemStats() {
            const key = this.storageKeys.systemStats;
            if (!localStorage.getItem(key)) {
                const initialData = {
                    version: this.dataVersion.current,
                    created: new Date().toISOString(),
                    stats: {
                        totalCorrections: 0,
                        accuracyRate: 0,
                        tokensSaved: 0,
                        responseTimeImprovement: 0,
                        cacheHitRate: 0
                    },
                    history: [],
                    metadata: {
                        lastUpdated: null,
                        updateCount: 0
                    }
                };
                this.setData(key, initialData);
            }
        }

        /**
         * 初始化用户偏好存储
         */
        initializeUserPreferences() {
            const key = this.storageKeys.userPreferences;
            if (!localStorage.getItem(key)) {
                const initialData = {
                    version: this.dataVersion.current,
                    created: new Date().toISOString(),
                    preferences: {
                        autoCorrection: false,
                        confidenceThreshold: 0.8,
                        notificationsEnabled: true,
                        learningMode: 'active'
                    },
                    metadata: {
                        lastModified: null
                    }
                };
                this.setData(key, initialData);
            }
        }

        /**
         * 获取数据
         * @param {string} key - 存储键
         * @returns {Object|null} 数据对象
         */
        getData(key) {
            try {
                const data = localStorage.getItem(key);
                return data ? JSON.parse(data) : null;
            } catch (error) {
                this.logger?.logError(`获取数据失败: ${key}`, error);
                return null;
            }
        }

        /**
         * 设置数据
         * @param {string} key - 存储键
         * @param {Object} data - 数据对象
         * @returns {boolean} 操作结果
         */
        setData(key, data) {
            try {
                // 检查存储大小限制
                const dataString = JSON.stringify(data);
                if (dataString.length > this.config.get('storage.maxStorageSize')) {
                    this.logger?.log(`数据大小超出限制: ${key}`, 'warn');
                    return false;
                }

                localStorage.setItem(key, dataString);
                return true;
            } catch (error) {
                this.logger?.logError(`设置数据失败: ${key}`, error);
                return false;
            }
        }

        /**
         * 删除数据
         * @param {string} key - 存储键
         */
        removeData(key) {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                this.logger?.logError(`删除数据失败: ${key}`, error);
            }
        }

        /**
         * 清空所有学习系统数据
         */
        clearAllData() {
            try {
                Object.values(this.storageKeys).forEach(key => {
                    this.removeData(key);
                });
                
                // 重新初始化
                this.initializeStorage();
                
                this.logger?.log('学习系统数据已清空并重新初始化', 'info');
            } catch (error) {
                this.logger?.logError('清空数据失败', error);
            }
        }

        /**
         * 导出所有数据
         * @returns {Object} 所有数据的对象
         */
        exportAllData() {
            const exportData = {
                version: this.dataVersion.current,
                exported: new Date().toISOString(),
                data: {}
            };

            Object.entries(this.storageKeys).forEach(([name, key]) => {
                exportData.data[name] = this.getData(key);
            });

            return exportData;
        }

        /**
         * 导入数据
         * @param {Object} importData - 导入的数据对象
         * @returns {boolean} 导入结果
         */
        importData(importData) {
            try {
                // 验证数据格式
                if (!importData.version || !importData.data) {
                    throw new Error('无效的导入数据格式');
                }

                // 备份当前数据
                this.backupData('before-import');

                // 导入数据
                Object.entries(importData.data).forEach(([name, data]) => {
                    if (this.storageKeys[name] && data) {
                        this.setData(this.storageKeys[name], data);
                    }
                });

                this.logger?.log('数据导入完成', 'success');
                return true;
                
            } catch (error) {
                this.logger?.logError('数据导入失败', error);
                return false;
            }
        }

        /**
         * 获取存储使用情况
         * @returns {Object} 存储使用统计
         */
        getStorageUsage() {
            const usage = {
                total: 0,
                byKey: {},
                percentage: 0
            };

            Object.entries(this.storageKeys).forEach(([name, key]) => {
                const data = localStorage.getItem(key);
                const size = data ? data.length : 0;
                usage.byKey[name] = size;
                usage.total += size;
            });

            const maxSize = this.config.get('storage.maxStorageSize');
            usage.percentage = (usage.total / maxSize) * 100;

            return usage;
        }
    }

    // 创建全局实例
    const learningStorageManager = new LearningStorageManager();

    // 导出到全局命名空间
    window.OTA.learningStorageManager = learningStorageManager;
    window.learningStorageManager = learningStorageManager; // 向后兼容

    // 工厂函数
    window.getLearningStorageManager = function() {
        return window.OTA.learningStorageManager || window.learningStorageManager;
    };

    console.log('学习系统存储管理器加载完成', {
        version: learningStorageManager.version,
        dataVersion: learningStorageManager.dataVersion.current
    });

})();

/**
 * 数据结构定义
 * 定义学习系统中使用的各种数据结构格式
 */

// 用户操作记录数据结构
window.OTA.LearningDataStructures = {
    /**
     * 用户操作记录结构
     */
    UserOperation: {
        id: '',                    // 唯一标识符
        timestamp: '',             // 操作时间戳
        type: '',                  // 操作类型: 'correction', 'validation', 'feedback'
        field: '',                 // 操作的字段名
        originalValue: '',         // 原始值
        correctedValue: '',        // 更正后的值
        context: {                 // 上下文信息
            orderText: '',         // 原始订单文本
            aiAnalysisResult: {},  // AI分析结果
            userAgent: '',         // 用户代理
            sessionId: ''          // 会话ID
        },
        confidence: 0,             // 用户操作的置信度
        metadata: {                // 元数据
            source: '',            // 操作来源
            duration: 0,           // 操作耗时
            errorType: '',         // 错误类型
            category: ''           // 分类
        }
    },

    /**
     * 学习规则结构
     */
    LearningRule: {
        id: '',                    // 规则唯一标识符
        type: '',                  // 规则类型: 'regex_pattern', 'value_mapping', 'context_rule', 'transformation'
        field: '',                 // 适用字段
        pattern: '',               // 匹配模式
        replacement: '',           // 替换值或转换逻辑
        conditions: [],            // 应用条件
        confidence: 0,             // 规则置信度
        priority: 0,               // 优先级
        statistics: {              // 统计信息
            created: '',           // 创建时间
            lastUsed: '',          // 最后使用时间
            usageCount: 0,         // 使用次数
            successRate: 0         // 成功率
        },
        metadata: {                // 元数据
            generatedFrom: [],     // 生成来源的操作ID列表
            category: '',          // 规则分类
            tags: []               // 标签
        }
    },

    /**
     * 系统统计结构
     */
    SystemStats: {
        period: '',                // 统计周期
        metrics: {
            totalCorrections: 0,   // 总更正次数
            accuracyRate: 0,       // 准确率
            tokensSaved: 0,        // 节省的Token数
            responseTime: 0,       // 平均响应时间
            cacheHitRate: 0,       // 缓存命中率
            learningEffectiveness: 0 // 学习效果
        },
        breakdown: {               // 详细分解
            byField: {},           // 按字段统计
            byErrorType: {},       // 按错误类型统计
            byTimeRange: {}        // 按时间范围统计
        },
        timestamp: ''              // 统计时间
    },

    /**
     * 用户偏好结构
     */
    UserPreferences: {
        learning: {
            autoCorrection: false,     // 自动更正
            confidenceThreshold: 0.8,  // 置信度阈值
            learningMode: 'active'     // 学习模式: 'active', 'passive', 'manual'
        },
        ui: {
            showNotifications: true,   // 显示通知
            showHistory: true,         // 显示历史
            compactMode: false         // 紧凑模式
        },
        privacy: {
            dataRetention: 30,         // 数据保留天数
            shareAnalytics: false      // 分享分析数据
        }
    }
};
